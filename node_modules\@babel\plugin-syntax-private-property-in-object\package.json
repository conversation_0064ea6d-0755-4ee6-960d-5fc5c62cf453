{"name": "@babel/plugin-syntax-private-property-in-object", "version": "7.14.5", "description": "Allow parsing of '#foo in obj' brand checks", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-private-property-in-object"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-private-property-in-object", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "exports": {".": "./lib/index.js"}, "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)"}