{"ast": null, "code": "import React from'react';import ReactDOM from'react-dom/client';import App from'./App';import{WebSocketProvider}from'./services/WebSocketContext';import'./App.css';import{jsx as _jsx}from\"react/jsx-runtime\";const root=ReactDOM.createRoot(document.getElementById('root'));root.render(/*#__PURE__*/_jsx(React.StrictMode,{children:/*#__PURE__*/_jsx(WebSocketProvider,{children:/*#__PURE__*/_jsx(App,{})})}));", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}