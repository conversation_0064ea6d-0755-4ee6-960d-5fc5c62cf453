{"name": "@babel/helper-create-regexp-features-plugin", "version": "7.27.1", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "description": "Compile ESNext Regular Expressions to ES5", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "main": "./lib/index.js", "publishConfig": {"access": "public"}, "keywords": ["babel", "babel-plugin"], "dependencies": {"@babel/helper-annotate-as-pure": "^7.27.1", "regexpu-core": "^6.2.0", "semver": "^6.3.1"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "devDependencies": {"@babel/core": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "type": "commonjs"}