{"version": 3, "names": ["_helperMemberExpressionToFunctions", "require", "_helperOptimiseCallExpression", "_core", "_traverse", "assignmentExpression", "callExpression", "cloneNode", "identifier", "memberExpression", "sequenceExpression", "stringLiteral", "thisExpression", "t", "exports", "environmentVisitor", "visitors", "skipAllButComputedKey", "path", "skip", "node", "computed", "context", "maybeQueue", "get", "visitor", "Super", "state", "parentPath", "isMemberExpression", "object", "handle", "unshadowSuperBindingVisitor", "Scopable", "refName", "binding", "scope", "getOwnBinding", "name", "rename", "specHandlers", "memoise", "superMember", "count", "property", "memo", "maybeGenerateMemoised", "memoiser", "set", "prop", "has", "_getPrototypeOfExpression", "objectRef", "getObjectRef", "targetRef", "isStatic", "isPrivateMethod", "file", "addHelper", "isDerivedConstructor", "numericLiteral", "_call", "args", "optional", "argsNode", "length", "isSpreadElement", "isIdentifier", "argument", "isArrayExpression", "arrayExpression", "call", "optionalCallExpression", "value", "isInStrictMode", "destructureSet", "buildCodeFrameError", "optionalCall", "delete", "template", "expression", "ast", "specHandlers_old", "_get", "proto", "booleanLiteral", "optimiseCall", "looseHandlers", "Object", "assign", "getSuperRef", "_getSuper<PERSON>ef", "_getSuperRef2", "ReplaceSupers", "constructor", "opts", "_opts$constantSuper", "methodPath", "isClassMethod", "kind", "superRef", "isObjectMethod", "static", "isStaticBlock", "isPrivate", "isMethod", "constant<PERSON>uper", "isLoose", "replace", "refToPreserve", "traverse", "handler", "availableHelper", "shouldSkip", "parent<PERSON><PERSON>", "memberExpressionToFunctions", "bind", "boundGet", "default"], "sources": ["../src/index.ts"], "sourcesContent": ["import type { File, NodePath, Scope } from \"@babel/core\";\nimport memberExpressionToFunctions from \"@babel/helper-member-expression-to-functions\";\nimport type { HandlerState } from \"@babel/helper-member-expression-to-functions\";\nimport optimiseCall from \"@babel/helper-optimise-call-expression\";\nimport { template, types as t } from \"@babel/core\";\nimport { visitors } from \"@babel/traverse\";\nconst {\n  assignmentExpression,\n  callExpression,\n  cloneNode,\n  identifier,\n  memberExpression,\n  sequenceExpression,\n  stringLiteral,\n  thisExpression,\n} = t;\n\nif (!process.env.BABEL_8_BREAKING && !USE_ESM && !IS_STANDALONE) {\n  // eslint-disable-next-line no-restricted-globals\n  exports.environmentVisitor = visitors.environmentVisitor({});\n  // eslint-disable-next-line no-restricted-globals\n  exports.skipAllButComputedKey = function skipAllButComputedKey(\n    path: NodePath<t.Method | t.ClassProperty>,\n  ) {\n    path.skip();\n    if (path.node.computed) {\n      path.context.maybeQueue(path.get(\"key\"));\n    }\n  };\n}\n\nconst visitor = visitors.environmentVisitor<\n  HandlerState<ReplaceState> & ReplaceState\n>({\n  Super(path, state) {\n    const { node, parentPath } = path;\n    if (!parentPath.isMemberExpression({ object: node })) return;\n    state.handle(parentPath);\n  },\n});\n\nconst unshadowSuperBindingVisitor = visitors.environmentVisitor<{\n  refName: string;\n}>({\n  Scopable(path, { refName }) {\n    // https://github.com/Zzzen/babel/pull/1#pullrequestreview-564833183\n    const binding = path.scope.getOwnBinding(refName);\n    if (binding && binding.identifier.name === refName) {\n      path.scope.rename(refName);\n    }\n  },\n});\n\ntype SharedState = {\n  file: File;\n  scope: Scope;\n  isDerivedConstructor: boolean;\n  isStatic: boolean;\n  isPrivateMethod: boolean;\n  getObjectRef: () => t.Identifier;\n  getSuperRef: () => t.Identifier;\n  // we dont need boundGet here, but memberExpressionToFunctions handler needs it.\n  boundGet: HandlerState[\"get\"];\n};\n\ntype Handler = HandlerState<SharedState> & SharedState;\ntype SuperMember = NodePath<\n  t.MemberExpression & {\n    object: t.Super;\n    property: Exclude<t.MemberExpression[\"property\"], t.PrivateName>;\n  }\n>;\n\nconst enum Flags {\n  Prototype = 0b1,\n  Call = 0b10,\n}\n\ninterface SpecHandler\n  extends Pick<\n    Handler,\n    | \"memoise\"\n    | \"get\"\n    | \"set\"\n    | \"destructureSet\"\n    | \"call\"\n    | \"optionalCall\"\n    | \"delete\"\n  > {\n  _get?(\n    this: Handler & SpecHandler,\n    superMember: SuperMember,\n  ): t.CallExpression;\n  _call?(\n    this: Handler & SpecHandler,\n    superMember: SuperMember,\n    args: t.CallExpression[\"arguments\"],\n    optional: boolean,\n  ): t.CallExpression | t.OptionalCallExpression;\n  _getPrototypeOfExpression(this: Handler & SpecHandler): t.CallExpression;\n  prop(this: Handler & SpecHandler, superMember: SuperMember): t.Expression;\n}\n\nconst specHandlers: SpecHandler = {\n  memoise(\n    this: Handler & SpecHandler,\n    superMember: SuperMember,\n    count: number,\n  ) {\n    const { scope, node } = superMember;\n    const { computed, property } = node;\n    if (!computed) {\n      return;\n    }\n\n    const memo = scope.maybeGenerateMemoised(property);\n    if (!memo) {\n      return;\n    }\n\n    this.memoiser.set(property, memo, count);\n  },\n\n  prop(this: Handler & SpecHandler, superMember: SuperMember) {\n    const { computed, property } = superMember.node;\n    if (this.memoiser.has(property)) {\n      return cloneNode(this.memoiser.get(property));\n    }\n\n    if (computed) {\n      return cloneNode(property);\n    }\n\n    return stringLiteral((property as t.Identifier).name);\n  },\n\n  /**\n   * Creates an expression which result is the proto of objectRef.\n   *\n   * @example <caption>isStatic === true</caption>\n   *\n   *   helpers.getPrototypeOf(CLASS)\n   *\n   * @example <caption>isStatic === false</caption>\n   *\n   *   helpers.getPrototypeOf(CLASS.prototype)\n   */\n  _getPrototypeOfExpression(this: Handler & SpecHandler) {\n    const objectRef = cloneNode(this.getObjectRef());\n    const targetRef =\n      this.isStatic || this.isPrivateMethod\n        ? objectRef\n        : memberExpression(objectRef, identifier(\"prototype\"));\n\n    return callExpression(this.file.addHelper(\"getPrototypeOf\"), [targetRef]);\n  },\n\n  get(this: Handler & SpecHandler, superMember: SuperMember) {\n    const objectRef = cloneNode(this.getObjectRef());\n    return callExpression(this.file.addHelper(\"superPropGet\"), [\n      this.isDerivedConstructor\n        ? sequenceExpression([thisExpression(), objectRef])\n        : objectRef,\n      this.prop(superMember),\n      thisExpression(),\n      ...(this.isStatic || this.isPrivateMethod\n        ? []\n        : [t.numericLiteral(Flags.Prototype)]),\n    ]);\n  },\n\n  _call(\n    this: Handler & SpecHandler,\n    superMember: SuperMember,\n    args: t.CallExpression[\"arguments\"],\n    optional: boolean,\n  ): t.CallExpression | t.OptionalCallExpression {\n    const objectRef = cloneNode(this.getObjectRef());\n    let argsNode: t.ArrayExpression | t.Identifier;\n    if (\n      args.length === 1 &&\n      t.isSpreadElement(args[0]) &&\n      (t.isIdentifier(args[0].argument) ||\n        t.isArrayExpression(args[0].argument))\n    ) {\n      argsNode = args[0].argument;\n    } else {\n      argsNode = t.arrayExpression(args as t.Expression[]);\n    }\n\n    const call = t.callExpression(this.file.addHelper(\"superPropGet\"), [\n      this.isDerivedConstructor\n        ? sequenceExpression([thisExpression(), objectRef])\n        : objectRef,\n      this.prop(superMember),\n      thisExpression(),\n      t.numericLiteral(\n        Flags.Call |\n          (this.isStatic || this.isPrivateMethod ? 0 : Flags.Prototype),\n      ),\n    ]);\n    if (optional) {\n      return t.optionalCallExpression(call, [argsNode], true);\n    }\n    return callExpression(call, [argsNode]);\n  },\n\n  set(\n    this: Handler & SpecHandler,\n    superMember: SuperMember,\n    value: t.Expression,\n  ) {\n    const objectRef = cloneNode(this.getObjectRef());\n\n    return callExpression(this.file.addHelper(\"superPropSet\"), [\n      this.isDerivedConstructor\n        ? sequenceExpression([thisExpression(), objectRef])\n        : objectRef,\n      this.prop(superMember),\n      value,\n      thisExpression(),\n      t.numericLiteral(superMember.isInStrictMode() ? 1 : 0),\n      ...(this.isStatic || this.isPrivateMethod ? [] : [t.numericLiteral(1)]),\n    ]);\n  },\n\n  destructureSet(this: Handler & SpecHandler, superMember: SuperMember) {\n    throw superMember.buildCodeFrameError(\n      `Destructuring to a super field is not supported yet.`,\n    );\n  },\n\n  call(\n    this: Handler & SpecHandler,\n    superMember: SuperMember,\n    args: t.CallExpression[\"arguments\"],\n  ) {\n    return this._call(superMember, args, false);\n  },\n\n  optionalCall(\n    this: Handler & SpecHandler,\n    superMember: SuperMember,\n    args: t.CallExpression[\"arguments\"],\n  ) {\n    return this._call(superMember, args, true);\n  },\n\n  delete(this: Handler & SpecHandler, superMember: SuperMember) {\n    if (superMember.node.computed) {\n      return sequenceExpression([\n        callExpression(this.file.addHelper(\"toPropertyKey\"), [\n          cloneNode(superMember.node.property),\n        ]),\n        template.expression.ast`\n          function () { throw new ReferenceError(\"'delete super[expr]' is invalid\"); }()\n        `,\n      ]);\n    } else {\n      return template.expression.ast`\n        function () { throw new ReferenceError(\"'delete super.prop' is invalid\"); }()\n      `;\n    }\n  },\n};\n\nconst specHandlers_old: SpecHandler = {\n  memoise(\n    this: Handler & SpecHandler,\n    superMember: SuperMember,\n    count: number,\n  ) {\n    const { scope, node } = superMember;\n    const { computed, property } = node;\n    if (!computed) {\n      return;\n    }\n\n    const memo = scope.maybeGenerateMemoised(property);\n    if (!memo) {\n      return;\n    }\n\n    this.memoiser.set(property, memo, count);\n  },\n\n  prop(this: Handler & SpecHandler, superMember: SuperMember) {\n    const { computed, property } = superMember.node;\n    if (this.memoiser.has(property)) {\n      return cloneNode(this.memoiser.get(property));\n    }\n\n    if (computed) {\n      return cloneNode(property);\n    }\n\n    return stringLiteral((property as t.Identifier).name);\n  },\n\n  /**\n   * Creates an expression which result is the proto of objectRef.\n   *\n   * @example <caption>isStatic === true</caption>\n   *\n   *   helpers.getPrototypeOf(CLASS)\n   *\n   * @example <caption>isStatic === false</caption>\n   *\n   *   helpers.getPrototypeOf(CLASS.prototype)\n   */\n  _getPrototypeOfExpression(this: Handler & SpecHandler) {\n    const objectRef = cloneNode(this.getObjectRef());\n    const targetRef =\n      this.isStatic || this.isPrivateMethod\n        ? objectRef\n        : memberExpression(objectRef, identifier(\"prototype\"));\n\n    return callExpression(this.file.addHelper(\"getPrototypeOf\"), [targetRef]);\n  },\n\n  get(this: Handler & SpecHandler, superMember: SuperMember) {\n    return this._get(superMember);\n  },\n\n  _get(this: Handler & SpecHandler, superMember: SuperMember) {\n    const proto = this._getPrototypeOfExpression();\n    return callExpression(this.file.addHelper(\"get\"), [\n      this.isDerivedConstructor\n        ? sequenceExpression([thisExpression(), proto])\n        : proto,\n      this.prop(superMember),\n      thisExpression(),\n    ]);\n  },\n\n  set(\n    this: Handler & SpecHandler,\n    superMember: SuperMember,\n    value: t.Expression,\n  ) {\n    const proto = this._getPrototypeOfExpression();\n\n    return callExpression(this.file.addHelper(\"set\"), [\n      this.isDerivedConstructor\n        ? sequenceExpression([thisExpression(), proto])\n        : proto,\n      this.prop(superMember),\n      value,\n      thisExpression(),\n      t.booleanLiteral(superMember.isInStrictMode()),\n    ]);\n  },\n\n  destructureSet(this: Handler & SpecHandler, superMember: SuperMember) {\n    throw superMember.buildCodeFrameError(\n      `Destructuring to a super field is not supported yet.`,\n    );\n  },\n\n  call(\n    this: Handler & SpecHandler,\n    superMember: SuperMember,\n    args: t.CallExpression[\"arguments\"],\n  ) {\n    return optimiseCall(this._get(superMember), thisExpression(), args, false);\n  },\n\n  optionalCall(\n    this: Handler & SpecHandler,\n    superMember: SuperMember,\n    args: t.CallExpression[\"arguments\"],\n  ) {\n    return optimiseCall(\n      this._get(superMember),\n      cloneNode(thisExpression()),\n      args,\n      true,\n    );\n  },\n\n  delete(this: Handler & SpecHandler, superMember: SuperMember) {\n    if (superMember.node.computed) {\n      return sequenceExpression([\n        callExpression(this.file.addHelper(\"toPropertyKey\"), [\n          cloneNode(superMember.node.property),\n        ]),\n        template.expression.ast`\n          function () { throw new ReferenceError(\"'delete super[expr]' is invalid\"); }()\n        `,\n      ]);\n    } else {\n      return template.expression.ast`\n        function () { throw new ReferenceError(\"'delete super.prop' is invalid\"); }()\n      `;\n    }\n  },\n};\n\nconst looseHandlers = {\n  ...specHandlers,\n\n  prop(this: Handler & typeof specHandlers, superMember: SuperMember) {\n    const { property } = superMember.node;\n    if (this.memoiser.has(property)) {\n      return cloneNode(this.memoiser.get(property));\n    }\n\n    return cloneNode(property);\n  },\n\n  get(this: Handler & typeof specHandlers, superMember: SuperMember) {\n    const { isStatic, getSuperRef } = this;\n    const { computed } = superMember.node;\n    const prop = this.prop(superMember);\n\n    let object;\n    if (isStatic) {\n      object =\n        getSuperRef() ??\n        memberExpression(identifier(\"Function\"), identifier(\"prototype\"));\n    } else {\n      object = memberExpression(\n        getSuperRef() ?? identifier(\"Object\"),\n        identifier(\"prototype\"),\n      );\n    }\n\n    return memberExpression(object, prop, computed);\n  },\n\n  set(\n    this: Handler & typeof specHandlers,\n    superMember: SuperMember,\n    value: t.Expression,\n  ) {\n    const { computed } = superMember.node;\n    const prop = this.prop(superMember);\n\n    return assignmentExpression(\n      \"=\",\n      memberExpression(thisExpression(), prop, computed),\n      value,\n    );\n  },\n\n  destructureSet(\n    this: Handler & typeof specHandlers,\n    superMember: SuperMember,\n  ) {\n    const { computed } = superMember.node;\n    const prop = this.prop(superMember);\n\n    return memberExpression(thisExpression(), prop, computed);\n  },\n\n  call(\n    this: Handler & typeof specHandlers,\n    superMember: SuperMember,\n    args: t.CallExpression[\"arguments\"],\n  ) {\n    return optimiseCall(this.get(superMember), thisExpression(), args, false);\n  },\n\n  optionalCall(\n    this: Handler & typeof specHandlers,\n    superMember: SuperMember,\n    args: t.CallExpression[\"arguments\"],\n  ) {\n    return optimiseCall(this.get(superMember), thisExpression(), args, true);\n  },\n};\n\ntype ReplaceSupersOptionsBase = {\n  methodPath: NodePath<\n    | t.ClassMethod\n    | t.ClassProperty\n    | t.ObjectMethod\n    | t.ClassPrivateMethod\n    | t.ClassPrivateProperty\n    | t.StaticBlock\n  >;\n  constantSuper?: boolean;\n  file: File;\n  // objectRef might have been shadowed in child scopes,\n  // in that case, we need to rename related variables.\n  refToPreserve?: t.Identifier;\n};\n\ntype ReplaceSupersOptions = ReplaceSupersOptionsBase &\n  (\n    | { objectRef?: undefined; getObjectRef: () => t.Node }\n    | { objectRef: t.Node; getObjectRef?: undefined }\n  ) &\n  (\n    | { superRef?: undefined; getSuperRef: () => t.Node }\n    | { superRef: t.Node; getSuperRef?: undefined }\n  );\n\ninterface ReplaceState {\n  file: File;\n  scope: Scope;\n  isDerivedConstructor: boolean;\n  isStatic: boolean;\n  isPrivateMethod: boolean;\n  getObjectRef: ReplaceSupers[\"getObjectRef\"];\n  getSuperRef: ReplaceSupers[\"getSuperRef\"];\n}\n\nexport default class ReplaceSupers {\n  constructor(opts: ReplaceSupersOptions) {\n    const path = opts.methodPath;\n\n    this.methodPath = path;\n    this.isDerivedConstructor =\n      path.isClassMethod({ kind: \"constructor\" }) && !!opts.superRef;\n    this.isStatic =\n      path.isObjectMethod() ||\n      // @ts-expect-error static is not in ClassPrivateMethod\n      path.node.static ||\n      path.isStaticBlock?.();\n    this.isPrivateMethod = path.isPrivate() && path.isMethod();\n\n    this.file = opts.file;\n    this.constantSuper = process.env.BABEL_8_BREAKING\n      ? opts.constantSuper\n      : // Fallback to isLoose for backward compatibility\n        (opts.constantSuper ?? (opts as any).isLoose);\n    this.opts = opts;\n  }\n\n  declare file: File;\n  declare isDerivedConstructor: boolean;\n  declare constantSuper: boolean;\n  declare isPrivateMethod: boolean;\n  declare isStatic: boolean;\n  declare methodPath: NodePath;\n  declare opts: ReplaceSupersOptions;\n\n  getObjectRef() {\n    return cloneNode(this.opts.objectRef || this.opts.getObjectRef());\n  }\n\n  getSuperRef() {\n    if (this.opts.superRef) return cloneNode(this.opts.superRef);\n    if (this.opts.getSuperRef) {\n      return cloneNode(this.opts.getSuperRef());\n    }\n  }\n\n  replace() {\n    const { methodPath } = this;\n    // https://github.com/babel/babel/issues/11994\n    if (this.opts.refToPreserve) {\n      methodPath.traverse(unshadowSuperBindingVisitor, {\n        refName: this.opts.refToPreserve.name,\n      });\n    }\n\n    const handler = this.constantSuper\n      ? looseHandlers\n      : process.env.BABEL_8_BREAKING ||\n          this.file.availableHelper(\"superPropSet\")\n        ? specHandlers\n        : specHandlers_old;\n\n    // todo: this should have been handled by the environmentVisitor,\n    // consider add visitSelf support for the path.traverse\n    // @ts-expect-error: Refine typings in packages/babel-traverse/src/types.ts\n    // shouldSkip is accepted in traverseNode\n    visitor.shouldSkip = (path: NodePath) => {\n      if (path.parentPath === methodPath) {\n        if (path.parentKey === \"decorators\" || path.parentKey === \"key\") {\n          return true;\n        }\n      }\n    };\n\n    memberExpressionToFunctions<ReplaceState>(methodPath, visitor, {\n      file: this.file,\n      scope: this.methodPath.scope,\n      isDerivedConstructor: this.isDerivedConstructor,\n      isStatic: this.isStatic,\n      isPrivateMethod: this.isPrivateMethod,\n      getObjectRef: this.getObjectRef.bind(this),\n      getSuperRef: this.getSuperRef.bind(this),\n      // we dont need boundGet here, but memberExpressionToFunctions handler needs it.\n      boundGet: handler.get,\n      ...handler,\n    });\n  }\n}\n"], "mappings": ";;;;;;AACA,IAAAA,kCAAA,GAAAC,OAAA;AAEA,IAAAC,6BAAA,GAAAD,OAAA;AACA,IAAAE,KAAA,GAAAF,OAAA;AACA,IAAAG,SAAA,GAAAH,OAAA;AACA,MAAM;EACJI,oBAAoB;EACpBC,cAAc;EACdC,SAAS;EACTC,UAAU;EACVC,gBAAgB;EAChBC,kBAAkB;EAClBC,aAAa;EACbC;AACF,CAAC,GAAGC,WAAC;AAE4D;EAE/DC,OAAO,CAACC,kBAAkB,GAAGC,kBAAQ,CAACD,kBAAkB,CAAC,CAAC,CAAC,CAAC;EAE5DD,OAAO,CAACG,qBAAqB,GAAG,SAASA,qBAAqBA,CAC5DC,IAA0C,EAC1C;IACAA,IAAI,CAACC,IAAI,CAAC,CAAC;IACX,IAAID,IAAI,CAACE,IAAI,CAACC,QAAQ,EAAE;MACtBH,IAAI,CAACI,OAAO,CAACC,UAAU,CAACL,IAAI,CAACM,GAAG,CAAC,KAAK,CAAC,CAAC;IAC1C;EACF,CAAC;AACH;AAEA,MAAMC,OAAO,GAAGT,kBAAQ,CAACD,kBAAkB,CAEzC;EACAW,KAAKA,CAACR,IAAI,EAAES,KAAK,EAAE;IACjB,MAAM;MAAEP,IAAI;MAAEQ;IAAW,CAAC,GAAGV,IAAI;IACjC,IAAI,CAACU,UAAU,CAACC,kBAAkB,CAAC;MAAEC,MAAM,EAAEV;IAAK,CAAC,CAAC,EAAE;IACtDO,KAAK,CAACI,MAAM,CAACH,UAAU,CAAC;EAC1B;AACF,CAAC,CAAC;AAEF,MAAMI,2BAA2B,GAAGhB,kBAAQ,CAACD,kBAAkB,CAE5D;EACDkB,QAAQA,CAACf,IAAI,EAAE;IAAEgB;EAAQ,CAAC,EAAE;IAE1B,MAAMC,OAAO,GAAGjB,IAAI,CAACkB,KAAK,CAACC,aAAa,CAACH,OAAO,CAAC;IACjD,IAAIC,OAAO,IAAIA,OAAO,CAAC3B,UAAU,CAAC8B,IAAI,KAAKJ,OAAO,EAAE;MAClDhB,IAAI,CAACkB,KAAK,CAACG,MAAM,CAACL,OAAO,CAAC;IAC5B;EACF;AACF,CAAC,CAAC;AAoDF,MAAMM,YAAyB,GAAG;EAChCC,OAAOA,CAELC,WAAwB,EACxBC,KAAa,EACb;IACA,MAAM;MAAEP,KAAK;MAAEhB;IAAK,CAAC,GAAGsB,WAAW;IACnC,MAAM;MAAErB,QAAQ;MAAEuB;IAAS,CAAC,GAAGxB,IAAI;IACnC,IAAI,CAACC,QAAQ,EAAE;MACb;IACF;IAEA,MAAMwB,IAAI,GAAGT,KAAK,CAACU,qBAAqB,CAACF,QAAQ,CAAC;IAClD,IAAI,CAACC,IAAI,EAAE;MACT;IACF;IAEA,IAAI,CAACE,QAAQ,CAACC,GAAG,CAACJ,QAAQ,EAAEC,IAAI,EAAEF,KAAK,CAAC;EAC1C,CAAC;EAEDM,IAAIA,CAA8BP,WAAwB,EAAE;IAC1D,MAAM;MAAErB,QAAQ;MAAEuB;IAAS,CAAC,GAAGF,WAAW,CAACtB,IAAI;IAC/C,IAAI,IAAI,CAAC2B,QAAQ,CAACG,GAAG,CAACN,QAAQ,CAAC,EAAE;MAC/B,OAAOrC,SAAS,CAAC,IAAI,CAACwC,QAAQ,CAACvB,GAAG,CAACoB,QAAQ,CAAC,CAAC;IAC/C;IAEA,IAAIvB,QAAQ,EAAE;MACZ,OAAOd,SAAS,CAACqC,QAAQ,CAAC;IAC5B;IAEA,OAAOjC,aAAa,CAAEiC,QAAQ,CAAkBN,IAAI,CAAC;EACvD,CAAC;EAaDa,yBAAyBA,CAAA,EAA8B;IACrD,MAAMC,SAAS,GAAG7C,SAAS,CAAC,IAAI,CAAC8C,YAAY,CAAC,CAAC,CAAC;IAChD,MAAMC,SAAS,GACb,IAAI,CAACC,QAAQ,IAAI,IAAI,CAACC,eAAe,GACjCJ,SAAS,GACT3C,gBAAgB,CAAC2C,SAAS,EAAE5C,UAAU,CAAC,WAAW,CAAC,CAAC;IAE1D,OAAOF,cAAc,CAAC,IAAI,CAACmD,IAAI,CAACC,SAAS,CAAC,gBAAgB,CAAC,EAAE,CAACJ,SAAS,CAAC,CAAC;EAC3E,CAAC;EAED9B,GAAGA,CAA8BkB,WAAwB,EAAE;IACzD,MAAMU,SAAS,GAAG7C,SAAS,CAAC,IAAI,CAAC8C,YAAY,CAAC,CAAC,CAAC;IAChD,OAAO/C,cAAc,CAAC,IAAI,CAACmD,IAAI,CAACC,SAAS,CAAC,cAAc,CAAC,EAAE,CACzD,IAAI,CAACC,oBAAoB,GACrBjD,kBAAkB,CAAC,CAACE,cAAc,CAAC,CAAC,EAAEwC,SAAS,CAAC,CAAC,GACjDA,SAAS,EACb,IAAI,CAACH,IAAI,CAACP,WAAW,CAAC,EACtB9B,cAAc,CAAC,CAAC,EAChB,IAAI,IAAI,CAAC2C,QAAQ,IAAI,IAAI,CAACC,eAAe,GACrC,EAAE,GACF,CAAC3C,WAAC,CAAC+C,cAAc,EAAgB,CAAC,CAAC,CAAC,CACzC,CAAC;EACJ,CAAC;EAEDC,KAAKA,CAEHnB,WAAwB,EACxBoB,IAAmC,EACnCC,QAAiB,EAC4B;IAC7C,MAAMX,SAAS,GAAG7C,SAAS,CAAC,IAAI,CAAC8C,YAAY,CAAC,CAAC,CAAC;IAChD,IAAIW,QAA0C;IAC9C,IACEF,IAAI,CAACG,MAAM,KAAK,CAAC,IACjBpD,WAAC,CAACqD,eAAe,CAACJ,IAAI,CAAC,CAAC,CAAC,CAAC,KACzBjD,WAAC,CAACsD,YAAY,CAACL,IAAI,CAAC,CAAC,CAAC,CAACM,QAAQ,CAAC,IAC/BvD,WAAC,CAACwD,iBAAiB,CAACP,IAAI,CAAC,CAAC,CAAC,CAACM,QAAQ,CAAC,CAAC,EACxC;MACAJ,QAAQ,GAAGF,IAAI,CAAC,CAAC,CAAC,CAACM,QAAQ;IAC7B,CAAC,MAAM;MACLJ,QAAQ,GAAGnD,WAAC,CAACyD,eAAe,CAACR,IAAsB,CAAC;IACtD;IAEA,MAAMS,IAAI,GAAG1D,WAAC,CAACP,cAAc,CAAC,IAAI,CAACmD,IAAI,CAACC,SAAS,CAAC,cAAc,CAAC,EAAE,CACjE,IAAI,CAACC,oBAAoB,GACrBjD,kBAAkB,CAAC,CAACE,cAAc,CAAC,CAAC,EAAEwC,SAAS,CAAC,CAAC,GACjDA,SAAS,EACb,IAAI,CAACH,IAAI,CAACP,WAAW,CAAC,EACtB9B,cAAc,CAAC,CAAC,EAChBC,WAAC,CAAC+C,cAAc,CACd,KACG,IAAI,CAACL,QAAQ,IAAI,IAAI,CAACC,eAAe,GAAG,CAAC,IAAkB,CAChE,CAAC,CACF,CAAC;IACF,IAAIO,QAAQ,EAAE;MACZ,OAAOlD,WAAC,CAAC2D,sBAAsB,CAACD,IAAI,EAAE,CAACP,QAAQ,CAAC,EAAE,IAAI,CAAC;IACzD;IACA,OAAO1D,cAAc,CAACiE,IAAI,EAAE,CAACP,QAAQ,CAAC,CAAC;EACzC,CAAC;EAEDhB,GAAGA,CAEDN,WAAwB,EACxB+B,KAAmB,EACnB;IACA,MAAMrB,SAAS,GAAG7C,SAAS,CAAC,IAAI,CAAC8C,YAAY,CAAC,CAAC,CAAC;IAEhD,OAAO/C,cAAc,CAAC,IAAI,CAACmD,IAAI,CAACC,SAAS,CAAC,cAAc,CAAC,EAAE,CACzD,IAAI,CAACC,oBAAoB,GACrBjD,kBAAkB,CAAC,CAACE,cAAc,CAAC,CAAC,EAAEwC,SAAS,CAAC,CAAC,GACjDA,SAAS,EACb,IAAI,CAACH,IAAI,CAACP,WAAW,CAAC,EACtB+B,KAAK,EACL7D,cAAc,CAAC,CAAC,EAChBC,WAAC,CAAC+C,cAAc,CAAClB,WAAW,CAACgC,cAAc,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EACtD,IAAI,IAAI,CAACnB,QAAQ,IAAI,IAAI,CAACC,eAAe,GAAG,EAAE,GAAG,CAAC3C,WAAC,CAAC+C,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CACxE,CAAC;EACJ,CAAC;EAEDe,cAAcA,CAA8BjC,WAAwB,EAAE;IACpE,MAAMA,WAAW,CAACkC,mBAAmB,CACnC,sDACF,CAAC;EACH,CAAC;EAEDL,IAAIA,CAEF7B,WAAwB,EACxBoB,IAAmC,EACnC;IACA,OAAO,IAAI,CAACD,KAAK,CAACnB,WAAW,EAAEoB,IAAI,EAAE,KAAK,CAAC;EAC7C,CAAC;EAEDe,YAAYA,CAEVnC,WAAwB,EACxBoB,IAAmC,EACnC;IACA,OAAO,IAAI,CAACD,KAAK,CAACnB,WAAW,EAAEoB,IAAI,EAAE,IAAI,CAAC;EAC5C,CAAC;EAEDgB,MAAMA,CAA8BpC,WAAwB,EAAE;IAC5D,IAAIA,WAAW,CAACtB,IAAI,CAACC,QAAQ,EAAE;MAC7B,OAAOX,kBAAkB,CAAC,CACxBJ,cAAc,CAAC,IAAI,CAACmD,IAAI,CAACC,SAAS,CAAC,eAAe,CAAC,EAAE,CACnDnD,SAAS,CAACmC,WAAW,CAACtB,IAAI,CAACwB,QAAQ,CAAC,CACrC,CAAC,EACFmC,cAAQ,CAACC,UAAU,CAACC,GAAG;AAC/B;AACA,SAAS,CACF,CAAC;IACJ,CAAC,MAAM;MACL,OAAOF,cAAQ,CAACC,UAAU,CAACC,GAAG;AACpC;AACA,OAAO;IACH;EACF;AACF,CAAC;AAED,MAAMC,gBAA6B,GAAG;EACpCzC,OAAOA,CAELC,WAAwB,EACxBC,KAAa,EACb;IACA,MAAM;MAAEP,KAAK;MAAEhB;IAAK,CAAC,GAAGsB,WAAW;IACnC,MAAM;MAAErB,QAAQ;MAAEuB;IAAS,CAAC,GAAGxB,IAAI;IACnC,IAAI,CAACC,QAAQ,EAAE;MACb;IACF;IAEA,MAAMwB,IAAI,GAAGT,KAAK,CAACU,qBAAqB,CAACF,QAAQ,CAAC;IAClD,IAAI,CAACC,IAAI,EAAE;MACT;IACF;IAEA,IAAI,CAACE,QAAQ,CAACC,GAAG,CAACJ,QAAQ,EAAEC,IAAI,EAAEF,KAAK,CAAC;EAC1C,CAAC;EAEDM,IAAIA,CAA8BP,WAAwB,EAAE;IAC1D,MAAM;MAAErB,QAAQ;MAAEuB;IAAS,CAAC,GAAGF,WAAW,CAACtB,IAAI;IAC/C,IAAI,IAAI,CAAC2B,QAAQ,CAACG,GAAG,CAACN,QAAQ,CAAC,EAAE;MAC/B,OAAOrC,SAAS,CAAC,IAAI,CAACwC,QAAQ,CAACvB,GAAG,CAACoB,QAAQ,CAAC,CAAC;IAC/C;IAEA,IAAIvB,QAAQ,EAAE;MACZ,OAAOd,SAAS,CAACqC,QAAQ,CAAC;IAC5B;IAEA,OAAOjC,aAAa,CAAEiC,QAAQ,CAAkBN,IAAI,CAAC;EACvD,CAAC;EAaDa,yBAAyBA,CAAA,EAA8B;IACrD,MAAMC,SAAS,GAAG7C,SAAS,CAAC,IAAI,CAAC8C,YAAY,CAAC,CAAC,CAAC;IAChD,MAAMC,SAAS,GACb,IAAI,CAACC,QAAQ,IAAI,IAAI,CAACC,eAAe,GACjCJ,SAAS,GACT3C,gBAAgB,CAAC2C,SAAS,EAAE5C,UAAU,CAAC,WAAW,CAAC,CAAC;IAE1D,OAAOF,cAAc,CAAC,IAAI,CAACmD,IAAI,CAACC,SAAS,CAAC,gBAAgB,CAAC,EAAE,CAACJ,SAAS,CAAC,CAAC;EAC3E,CAAC;EAED9B,GAAGA,CAA8BkB,WAAwB,EAAE;IACzD,OAAO,IAAI,CAACyC,IAAI,CAACzC,WAAW,CAAC;EAC/B,CAAC;EAEDyC,IAAIA,CAA8BzC,WAAwB,EAAE;IAC1D,MAAM0C,KAAK,GAAG,IAAI,CAACjC,yBAAyB,CAAC,CAAC;IAC9C,OAAO7C,cAAc,CAAC,IAAI,CAACmD,IAAI,CAACC,SAAS,CAAC,KAAK,CAAC,EAAE,CAChD,IAAI,CAACC,oBAAoB,GACrBjD,kBAAkB,CAAC,CAACE,cAAc,CAAC,CAAC,EAAEwE,KAAK,CAAC,CAAC,GAC7CA,KAAK,EACT,IAAI,CAACnC,IAAI,CAACP,WAAW,CAAC,EACtB9B,cAAc,CAAC,CAAC,CACjB,CAAC;EACJ,CAAC;EAEDoC,GAAGA,CAEDN,WAAwB,EACxB+B,KAAmB,EACnB;IACA,MAAMW,KAAK,GAAG,IAAI,CAACjC,yBAAyB,CAAC,CAAC;IAE9C,OAAO7C,cAAc,CAAC,IAAI,CAACmD,IAAI,CAACC,SAAS,CAAC,KAAK,CAAC,EAAE,CAChD,IAAI,CAACC,oBAAoB,GACrBjD,kBAAkB,CAAC,CAACE,cAAc,CAAC,CAAC,EAAEwE,KAAK,CAAC,CAAC,GAC7CA,KAAK,EACT,IAAI,CAACnC,IAAI,CAACP,WAAW,CAAC,EACtB+B,KAAK,EACL7D,cAAc,CAAC,CAAC,EAChBC,WAAC,CAACwE,cAAc,CAAC3C,WAAW,CAACgC,cAAc,CAAC,CAAC,CAAC,CAC/C,CAAC;EACJ,CAAC;EAEDC,cAAcA,CAA8BjC,WAAwB,EAAE;IACpE,MAAMA,WAAW,CAACkC,mBAAmB,CACnC,sDACF,CAAC;EACH,CAAC;EAEDL,IAAIA,CAEF7B,WAAwB,EACxBoB,IAAmC,EACnC;IACA,OAAO,IAAAwB,qCAAY,EAAC,IAAI,CAACH,IAAI,CAACzC,WAAW,CAAC,EAAE9B,cAAc,CAAC,CAAC,EAAEkD,IAAI,EAAE,KAAK,CAAC;EAC5E,CAAC;EAEDe,YAAYA,CAEVnC,WAAwB,EACxBoB,IAAmC,EACnC;IACA,OAAO,IAAAwB,qCAAY,EACjB,IAAI,CAACH,IAAI,CAACzC,WAAW,CAAC,EACtBnC,SAAS,CAACK,cAAc,CAAC,CAAC,CAAC,EAC3BkD,IAAI,EACJ,IACF,CAAC;EACH,CAAC;EAEDgB,MAAMA,CAA8BpC,WAAwB,EAAE;IAC5D,IAAIA,WAAW,CAACtB,IAAI,CAACC,QAAQ,EAAE;MAC7B,OAAOX,kBAAkB,CAAC,CACxBJ,cAAc,CAAC,IAAI,CAACmD,IAAI,CAACC,SAAS,CAAC,eAAe,CAAC,EAAE,CACnDnD,SAAS,CAACmC,WAAW,CAACtB,IAAI,CAACwB,QAAQ,CAAC,CACrC,CAAC,EACFmC,cAAQ,CAACC,UAAU,CAACC,GAAG;AAC/B;AACA,SAAS,CACF,CAAC;IACJ,CAAC,MAAM;MACL,OAAOF,cAAQ,CAACC,UAAU,CAACC,GAAG;AACpC;AACA,OAAO;IACH;EACF;AACF,CAAC;AAED,MAAMM,aAAa,GAAAC,MAAA,CAAAC,MAAA,KACdjD,YAAY;EAEfS,IAAIA,CAAsCP,WAAwB,EAAE;IAClE,MAAM;MAAEE;IAAS,CAAC,GAAGF,WAAW,CAACtB,IAAI;IACrC,IAAI,IAAI,CAAC2B,QAAQ,CAACG,GAAG,CAACN,QAAQ,CAAC,EAAE;MAC/B,OAAOrC,SAAS,CAAC,IAAI,CAACwC,QAAQ,CAACvB,GAAG,CAACoB,QAAQ,CAAC,CAAC;IAC/C;IAEA,OAAOrC,SAAS,CAACqC,QAAQ,CAAC;EAC5B,CAAC;EAEDpB,GAAGA,CAAsCkB,WAAwB,EAAE;IACjE,MAAM;MAAEa,QAAQ;MAAEmC;IAAY,CAAC,GAAG,IAAI;IACtC,MAAM;MAAErE;IAAS,CAAC,GAAGqB,WAAW,CAACtB,IAAI;IACrC,MAAM6B,IAAI,GAAG,IAAI,CAACA,IAAI,CAACP,WAAW,CAAC;IAEnC,IAAIZ,MAAM;IACV,IAAIyB,QAAQ,EAAE;MAAA,IAAAoC,YAAA;MACZ7D,MAAM,IAAA6D,YAAA,GACJD,WAAW,CAAC,CAAC,YAAAC,YAAA,GACblF,gBAAgB,CAACD,UAAU,CAAC,UAAU,CAAC,EAAEA,UAAU,CAAC,WAAW,CAAC,CAAC;IACrE,CAAC,MAAM;MAAA,IAAAoF,aAAA;MACL9D,MAAM,GAAGrB,gBAAgB,EAAAmF,aAAA,GACvBF,WAAW,CAAC,CAAC,YAAAE,aAAA,GAAIpF,UAAU,CAAC,QAAQ,CAAC,EACrCA,UAAU,CAAC,WAAW,CACxB,CAAC;IACH;IAEA,OAAOC,gBAAgB,CAACqB,MAAM,EAAEmB,IAAI,EAAE5B,QAAQ,CAAC;EACjD,CAAC;EAED2B,GAAGA,CAEDN,WAAwB,EACxB+B,KAAmB,EACnB;IACA,MAAM;MAAEpD;IAAS,CAAC,GAAGqB,WAAW,CAACtB,IAAI;IACrC,MAAM6B,IAAI,GAAG,IAAI,CAACA,IAAI,CAACP,WAAW,CAAC;IAEnC,OAAOrC,oBAAoB,CACzB,GAAG,EACHI,gBAAgB,CAACG,cAAc,CAAC,CAAC,EAAEqC,IAAI,EAAE5B,QAAQ,CAAC,EAClDoD,KACF,CAAC;EACH,CAAC;EAEDE,cAAcA,CAEZjC,WAAwB,EACxB;IACA,MAAM;MAAErB;IAAS,CAAC,GAAGqB,WAAW,CAACtB,IAAI;IACrC,MAAM6B,IAAI,GAAG,IAAI,CAACA,IAAI,CAACP,WAAW,CAAC;IAEnC,OAAOjC,gBAAgB,CAACG,cAAc,CAAC,CAAC,EAAEqC,IAAI,EAAE5B,QAAQ,CAAC;EAC3D,CAAC;EAEDkD,IAAIA,CAEF7B,WAAwB,EACxBoB,IAAmC,EACnC;IACA,OAAO,IAAAwB,qCAAY,EAAC,IAAI,CAAC9D,GAAG,CAACkB,WAAW,CAAC,EAAE9B,cAAc,CAAC,CAAC,EAAEkD,IAAI,EAAE,KAAK,CAAC;EAC3E,CAAC;EAEDe,YAAYA,CAEVnC,WAAwB,EACxBoB,IAAmC,EACnC;IACA,OAAO,IAAAwB,qCAAY,EAAC,IAAI,CAAC9D,GAAG,CAACkB,WAAW,CAAC,EAAE9B,cAAc,CAAC,CAAC,EAAEkD,IAAI,EAAE,IAAI,CAAC;EAC1E;AAAC,EACF;AAsCc,MAAM+B,aAAa,CAAC;EACjCC,WAAWA,CAACC,IAA0B,EAAE;IAAA,IAAAC,mBAAA;IACtC,MAAM9E,IAAI,GAAG6E,IAAI,CAACE,UAAU;IAE5B,IAAI,CAACA,UAAU,GAAG/E,IAAI;IACtB,IAAI,CAACyC,oBAAoB,GACvBzC,IAAI,CAACgF,aAAa,CAAC;MAAEC,IAAI,EAAE;IAAc,CAAC,CAAC,IAAI,CAAC,CAACJ,IAAI,CAACK,QAAQ;IAChE,IAAI,CAAC7C,QAAQ,GACXrC,IAAI,CAACmF,cAAc,CAAC,CAAC,IAErBnF,IAAI,CAACE,IAAI,CAACkF,MAAM,KAChBpF,IAAI,CAACqF,aAAa,oBAAlBrF,IAAI,CAACqF,aAAa,CAAG,CAAC;IACxB,IAAI,CAAC/C,eAAe,GAAGtC,IAAI,CAACsF,SAAS,CAAC,CAAC,IAAItF,IAAI,CAACuF,QAAQ,CAAC,CAAC;IAE1D,IAAI,CAAChD,IAAI,GAAGsC,IAAI,CAACtC,IAAI;IACrB,IAAI,CAACiD,aAAa,IAAAV,mBAAA,GAGbD,IAAI,CAACW,aAAa,YAAAV,mBAAA,GAAKD,IAAI,CAASY,OAAQ;IACjD,IAAI,CAACZ,IAAI,GAAGA,IAAI;EAClB;EAUA1C,YAAYA,CAAA,EAAG;IACb,OAAO9C,SAAS,CAAC,IAAI,CAACwF,IAAI,CAAC3C,SAAS,IAAI,IAAI,CAAC2C,IAAI,CAAC1C,YAAY,CAAC,CAAC,CAAC;EACnE;EAEAqC,WAAWA,CAAA,EAAG;IACZ,IAAI,IAAI,CAACK,IAAI,CAACK,QAAQ,EAAE,OAAO7F,SAAS,CAAC,IAAI,CAACwF,IAAI,CAACK,QAAQ,CAAC;IAC5D,IAAI,IAAI,CAACL,IAAI,CAACL,WAAW,EAAE;MACzB,OAAOnF,SAAS,CAAC,IAAI,CAACwF,IAAI,CAACL,WAAW,CAAC,CAAC,CAAC;IAC3C;EACF;EAEAkB,OAAOA,CAAA,EAAG;IACR,MAAM;MAAEX;IAAW,CAAC,GAAG,IAAI;IAE3B,IAAI,IAAI,CAACF,IAAI,CAACc,aAAa,EAAE;MAC3BZ,UAAU,CAACa,QAAQ,CAAC9E,2BAA2B,EAAE;QAC/CE,OAAO,EAAE,IAAI,CAAC6D,IAAI,CAACc,aAAa,CAACvE;MACnC,CAAC,CAAC;IACJ;IAEA,MAAMyE,OAAO,GAAG,IAAI,CAACL,aAAa,GAC9BnB,aAAa,GAEX,IAAI,CAAC9B,IAAI,CAACuD,eAAe,CAAC,cAAc,CAAC,GACzCxE,YAAY,GACZ0C,gBAAgB;IAMtBzD,OAAO,CAACwF,UAAU,GAAI/F,IAAc,IAAK;MACvC,IAAIA,IAAI,CAACU,UAAU,KAAKqE,UAAU,EAAE;QAClC,IAAI/E,IAAI,CAACgG,SAAS,KAAK,YAAY,IAAIhG,IAAI,CAACgG,SAAS,KAAK,KAAK,EAAE;UAC/D,OAAO,IAAI;QACb;MACF;IACF,CAAC;IAED,IAAAC,0CAA2B,EAAelB,UAAU,EAAExE,OAAO,EAAA+D,MAAA,CAAAC,MAAA;MAC3DhC,IAAI,EAAE,IAAI,CAACA,IAAI;MACfrB,KAAK,EAAE,IAAI,CAAC6D,UAAU,CAAC7D,KAAK;MAC5BuB,oBAAoB,EAAE,IAAI,CAACA,oBAAoB;MAC/CJ,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBC,eAAe,EAAE,IAAI,CAACA,eAAe;MACrCH,YAAY,EAAE,IAAI,CAACA,YAAY,CAAC+D,IAAI,CAAC,IAAI,CAAC;MAC1C1B,WAAW,EAAE,IAAI,CAACA,WAAW,CAAC0B,IAAI,CAAC,IAAI,CAAC;MAExCC,QAAQ,EAAEN,OAAO,CAACvF;IAAG,GAClBuF,OAAO,CACX,CAAC;EACJ;AACF;AAACjG,OAAA,CAAAwG,OAAA,GAAAzB,aAAA", "ignoreList": []}