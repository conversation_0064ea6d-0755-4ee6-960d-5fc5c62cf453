<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fraud Detection Platform - Test Interface</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background: #667eea;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #5a6fd8;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
        }
        .low-risk {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .medium-risk {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .high-risk {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🛡️ Fraud Detection Platform</h1>
        <p>Real-time transaction fraud scoring system</p>
    </div>

    <div class="card">
        <h2>Service Status</h2>
        <div id="serviceStatus">
            <div class="status" id="modelStatus">Model Service: Checking...</div>
            <div class="status" id="ingestStatus">Ingest Service: Checking...</div>
        </div>
    </div>

    <div class="card">
        <h2>Test Transaction</h2>
        <form id="transactionForm">
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                <div>
                    <div class="form-group">
                        <label for="type">Transaction Type:</label>
                        <select id="type" required>
                            <option value="PAYMENT">Payment</option>
                            <option value="TRANSFER">Transfer</option>
                            <option value="CASH_OUT">Cash Out</option>
                            <option value="DEBIT">Debit</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="amount">Amount ($):</label>
                        <input type="number" id="amount" step="0.01" min="0" value="1000" required>
                    </div>
                    <div class="form-group">
                        <label for="nameOrig">Origin Account:</label>
                        <input type="text" id="nameOrig" value="C123456789" required>
                    </div>
                    <div class="form-group">
                        <label for="oldbalanceOrg">Old Balance (Origin):</label>
                        <input type="number" id="oldbalanceOrg" step="0.01" min="0" value="5000" required>
                    </div>
                </div>
                <div>
                    <div class="form-group">
                        <label for="newbalanceOrig">New Balance (Origin):</label>
                        <input type="number" id="newbalanceOrig" step="0.01" min="0" value="4000" required>
                    </div>
                    <div class="form-group">
                        <label for="nameDest">Destination Account:</label>
                        <input type="text" id="nameDest" value="M987654321" required>
                    </div>
                    <div class="form-group">
                        <label for="oldbalanceDest">Old Balance (Destination):</label>
                        <input type="number" id="oldbalanceDest" step="0.01" min="0" value="0" required>
                    </div>
                    <div class="form-group">
                        <label for="newbalanceDest">New Balance (Destination):</label>
                        <input type="number" id="newbalanceDest" step="0.01" min="0" value="1000" required>
                    </div>
                </div>
            </div>
            <button type="submit">🔍 Check for Fraud</button>
        </form>
        
        <div id="result"></div>
    </div>

    <script>
        const MODEL_SERVICE_URL = 'http://localhost:8001';
        const INGEST_SERVICE_URL = 'http://localhost:9001';

        // Check service status
        async function checkServiceStatus() {
            // Check Model Service
            try {
                const response = await fetch(`${MODEL_SERVICE_URL}/health`);
                if (response.ok) {
                    document.getElementById('modelStatus').textContent = 'Model Service: ✅ Online';
                    document.getElementById('modelStatus').className = 'status success';
                } else {
                    throw new Error('Service unavailable');
                }
            } catch (error) {
                document.getElementById('modelStatus').textContent = 'Model Service: ❌ Offline';
                document.getElementById('modelStatus').className = 'status error';
            }

            // Check Ingest Service
            try {
                const response = await fetch(`${INGEST_SERVICE_URL}/health`);
                if (response.ok) {
                    document.getElementById('ingestStatus').textContent = 'Ingest Service: ✅ Online';
                    document.getElementById('ingestStatus').className = 'status success';
                } else {
                    throw new Error('Service unavailable');
                }
            } catch (error) {
                document.getElementById('ingestStatus').textContent = 'Ingest Service: ❌ Offline (using Model Service directly)';
                document.getElementById('ingestStatus').className = 'status error';
            }
        }

        // Submit transaction for fraud scoring
        document.getElementById('transactionForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const transaction = {
                transaction_id: `test_${Date.now()}`,
                step: 1,
                type: document.getElementById('type').value,
                amount: parseFloat(document.getElementById('amount').value),
                nameOrig: document.getElementById('nameOrig').value,
                oldbalanceOrg: parseFloat(document.getElementById('oldbalanceOrg').value),
                newbalanceOrig: parseFloat(document.getElementById('newbalanceOrig').value),
                nameDest: document.getElementById('nameDest').value,
                oldbalanceDest: parseFloat(document.getElementById('oldbalanceDest').value),
                newbalanceDest: parseFloat(document.getElementById('newbalanceDest').value)
            };

            try {
                const response = await fetch(`${MODEL_SERVICE_URL}/score`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ transactions: [transaction] })
                });

                if (response.ok) {
                    const result = await response.json();
                    const riskScore = result.results[0].risk;
                    displayResult(transaction, riskScore);
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                document.getElementById('result').innerHTML = `
                    <div class="result high-risk">
                        <h3>❌ Error</h3>
                        <p>Failed to score transaction: ${error.message}</p>
                    </div>
                `;
            }
        });

        function displayResult(transaction, riskScore) {
            const percentage = (riskScore * 100).toFixed(1);
            let riskClass, riskIcon, riskText;

            if (riskScore >= 0.8) {
                riskClass = 'high-risk';
                riskIcon = '🚨';
                riskText = 'HIGH RISK';
            } else if (riskScore >= 0.5) {
                riskClass = 'medium-risk';
                riskIcon = '⚠️';
                riskText = 'MEDIUM RISK';
            } else {
                riskClass = 'low-risk';
                riskIcon = '✅';
                riskText = 'LOW RISK';
            }

            document.getElementById('result').innerHTML = `
                <div class="result ${riskClass}">
                    <h3>${riskIcon} ${riskText}</h3>
                    <p><strong>Risk Score:</strong> ${percentage}%</p>
                    <p><strong>Transaction ID:</strong> ${transaction.transaction_id}</p>
                    <p><strong>Type:</strong> ${transaction.type}</p>
                    <p><strong>Amount:</strong> $${transaction.amount.toLocaleString()}</p>
                    <p><strong>From:</strong> ${transaction.nameOrig} → <strong>To:</strong> ${transaction.nameDest}</p>
                </div>
            `;
        }

        // Check service status on page load
        checkServiceStatus();
        
        // Refresh status every 30 seconds
        setInterval(checkServiceStatus, 30000);
    </script>
</body>
</html>
